import React, { useEffect, useState } from 'react';
import { Platform, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../Text';

export type PositionFilter = 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';

interface StyledProps {
  theme: DefaultTheme;
}

const FilterContainer = styled.View`
  margin-bottom: 16px;
  z-index: 100; /* Higher z-index to ensure dropdown appears above list */
`;

interface DropdownContainerProps {
  $ref?: React.RefObject<View>;
}

const DropdownContainer = styled.View<DropdownContainerProps>`
  position: relative;
  z-index: 101; /* Higher than FilterContainer */
  width: 300px;
  padding: 10px 0px 0px 15px;
`;

const DropdownButton = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const DropdownText = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const DropdownOptions = styled.View`
  position: absolute;
  top: 100%;
  left: 15px;
  right: 0;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props: StyledProps) => props.theme.colors.border};
  margin-top: 4px;
  z-index: 102; /* Higher than DropdownContainer */
  overflow: hidden;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
    web: `
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    `,
  })}
`;

interface OptionItemProps extends StyledProps {
  isSelected: boolean;
}

const OptionItem = styled.TouchableOpacity<OptionItemProps>`
  padding: 10px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '20' : 'transparent'};
`;

const OptionText = styled(Text)<OptionItemProps>`
  font-family: ${(props) =>
    props.isSelected ? props.theme.typography.bold : props.theme.typography.regular};
  font-size: 14px;
  color: ${(props) =>
    props.isSelected ? props.theme.colors.primary : props.theme.colors.text.primary};
`;

interface PositionFilterProps {
  positionFilter: PositionFilter;
  onPositionSelect: (position: PositionFilter) => void;
}

const PositionFilter: React.FC<PositionFilterProps> = ({ positionFilter, onPositionSelect }) => {
  const [showPositionDropdown, setShowPositionDropdown] = useState(false);

  // Reference to the dropdown container for click-outside handling
  const dropdownRef = React.useRef<View | null>(null);

  // For web platforms, we can add a click-outside handler
  useEffect(() => {
    if (Platform.OS === 'web' && typeof document !== 'undefined') {
      const handleClickOutside = (event: any) => {
        if (
          dropdownRef.current &&
          // @ts-ignore - contains is available in web but not in RN types
          !dropdownRef.current.contains(event.target) &&
          showPositionDropdown
        ) {
          setShowPositionDropdown(false);
        }
      };

      // Add event listener when dropdown is open
      if (showPositionDropdown) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      // Clean up event listener
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
    // Add an empty return for non-web platforms
    return () => {};
  }, [showPositionDropdown]);

  // Handle window resize to update dropdown position
  useEffect(() => {
    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      const handleResize = () => {
        // Close dropdown on resize to prevent positioning issues
        if (showPositionDropdown) {
          setShowPositionDropdown(false);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [showPositionDropdown]);

  const togglePositionDropdown = () => {
    setShowPositionDropdown(!showPositionDropdown);
  };

  const handlePositionSelect = (position: PositionFilter) => {
    onPositionSelect(position);
    setShowPositionDropdown(false);
  };

  return (
    <FilterContainer style={Platform.OS === 'web' ? { position: 'relative', zIndex: 1000 } : {}}>
      <DropdownContainer $ref={dropdownRef as React.RefObject<View>}>
        <DropdownButton onPress={togglePositionDropdown}>
          <DropdownText>Show Attributes: {positionFilter}</DropdownText>
          <DropdownText>{showPositionDropdown ? '▲' : '▼'}</DropdownText>
        </DropdownButton>
        {showPositionDropdown && (
          <DropdownOptions
            style={Platform.OS === 'web' ? { position: 'absolute', zIndex: 1001 } : {}}
          >
            {(['All', 'Goalkeeper', 'Defender', 'Midfielder', 'Attacker'] as PositionFilter[]).map(
              (position) => (
                <OptionItem
                  key={position}
                  onPress={() => handlePositionSelect(position)}
                  isSelected={positionFilter === position}
                >
                  <OptionText isSelected={positionFilter === position}>{position}</OptionText>
                </OptionItem>
              )
            )}
          </DropdownOptions>
        )}
      </DropdownContainer>
    </FilterContainer>
  );
};

export default PositionFilter;
